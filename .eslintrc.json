{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@tanstack/eslint-plugin-query/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "eslint-plugin-react", "@tanstack/query", "react-hooks"], "settings": {"react": {"version": "18.1"}}, "rules": {"react/react-in-jsx-scope": "off", "no-undef": "off", "no-unused-vars": "off", "react/jsx-no-target-blank": "off", "react/no-unescaped-entities": "off", "@tanstack/query/exhaustive-deps": "error", "@tanstack/query/no-rest-destructuring": "warn", "@tanstack/query/stable-query-client": "error", "react-hooks/exhaustive-deps": "warn"}}