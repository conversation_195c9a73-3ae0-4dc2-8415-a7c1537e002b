import BoxErrorMessage from 'components/partials/BoxErrorMessage';
import Footer from 'components/partials/Footer';
import Header from 'components/partials/Header';
import { AUTH_KEYS } from 'constants/auth';
import { OPERATION_NAME, QUERY_KEY } from 'constants/common';
import includes from 'lodash/includes';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import startsWith from 'lodash/startsWith';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import React from 'react';
import { LOGOUT, PROFILE, PROFILE_GROUP } from 'services/UserService';
import { useAuthStore } from 'stores/authStore';
import { LogoutQuery, ProfileQuery, UserRoleNames } from 'types/User';
import { getLocalStorage } from 'utils/localStorage';
import { useGraphQLQuery } from '../../hooks/useGraphQLQuery';
import { getHierarchyActions, getUniqueActions } from '../../services/ActionService';
import { TemplateTypeNames } from '../../types/Template';

export function PrivateRoute({ children }: Readonly<{ children: JSX.Element }>) {
    const [logoutState, setLogoutState] = useState(false);
    const location = useLocation();
    const accessToken = getLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
    const [canAccess, setCanAccess] = useState(false);
    const user = useAuthStore((state) => state.user);
    const authorized = useAuthStore((state) => state.authorized);
    const unauthorized = useAuthStore((state) => state.unauthorized);
    const navigate = useNavigate();
    const { t } = useTranslation();

    useGraphQLQuery<LogoutQuery>([QUERY_KEY.LOGOUT, logoutState], LOGOUT, undefined, OPERATION_NAME.LOGOUT, {
        enabled: logoutState,
    });

    const handleLogout = () => {
        setLogoutState(true);
        unauthorized();
    };

    const { data: profile, isLoading: isLoadingProfile } = useGraphQLQuery<ProfileQuery>(
        [QUERY_KEY.PROFILE, accessToken],
        PROFILE,
        undefined,
        '',
        {
            enabled: !!accessToken,
        }
    );

    const { data: profileGroup } = useGraphQLQuery<ProfileQuery>(
        [QUERY_KEY.PROFILE, accessToken, 'group'],
        PROFILE_GROUP,
        undefined,
        '',
        {
            enabled: !!accessToken,
        }
    );

    const userGroups = useMemo(() => profileGroup?.auth_profile?.groups || [], [profileGroup]);
    const uniqueActions = useMemo(() => getUniqueActions(userGroups), [userGroups]);
    const hierarchyActions = useMemo(() => getHierarchyActions(uniqueActions ?? []), [uniqueActions]);

    useEffect(() => {
        if (!isLoadingProfile) {
            if (profile) {
                authorized(profile.auth_profile);
            } else {
                setLogoutState(true);
                unauthorized();
            }
        }
    }, [setLogoutState, isLoadingProfile, profile, authorized, unauthorized]);

    useEffect(() => {
        let url = location.pathname;
        let isAccess = includes(['/dashboard', '/user/profile', '/action'], url);
        //let isAccess = true;
        if (!isAccess) {
            isAccess = !!find(uniqueActions, { url });

            // Kiểm tra URL có tiền tố trùng với URL của phần tử có parent_id null và children rỗng
            if (!isAccess) {
                const parentActions = uniqueActions?.filter(
                    (action) => action.parent_id === null && (!action.children || action.children.length === 0)
                );
                isAccess = !!parentActions?.some(
                    (action) => startsWith(url, action.url) && (url === action.url || startsWith(url, `${action.url}/`))
                );
            }
            if (!isAccess && (startsWith(url, '/user/add/') || startsWith(url, '/user/edit/'))) {
                UserRoleNames.forEach((item) => {
                    url = `/user/list/${item.name}`;
                    if (!isAccess) isAccess = !!find(uniqueActions, { url });
                });
            }
            if (!isAccess && (startsWith(url, '/template/add/') || startsWith(url, '/template/edit/'))) {
                TemplateTypeNames.forEach((item) => {
                    url = `/template/list/${item.name}`;
                    if (!isAccess) isAccess = !!find(uniqueActions, { url });
                });
            }
            if (!isAccess && (startsWith(url, '/component/add') || startsWith(url, '/component/edit/'))) {
                url = '/component';
                if (!isAccess) isAccess = !!find(uniqueActions, { url });
            }
        }
        setCanAccess(isAccess);
    }, [uniqueActions, location]);

    if (!isEmpty(accessToken)) {
        return (
            <>
                <Header actions={hierarchyActions} user={user} handleLogout={handleLogout} />
                <div className="app-content content chat-application">
                    <div className="content-overlay" />
                    <div className="header-navbar-shadow" />
                    {!isLoadingProfile && canAccess && (
                        <div className="content-wrapper container-xxl p-0">
                            {React.cloneElement(children, { actions: hierarchyActions })}
                        </div>
                    )}
                    {!isLoadingProfile && !canAccess && (
                        <div className="content-wrapper container-xxl p-0">
                            <div className="content-body">
                                <BoxErrorMessage title={t('notifications')} messages={[t('notFoundDesc')]} />
                            </div>
                        </div>
                    )}
                </div>
                <Footer />
            </>
        );
    }
    return <Navigate to={`/?url=${location.pathname}`} />;
}
