{
    // https://palantir.github.io/tslint/rules/
    "rules": {
        "object-literal-sort-keys": false,
        "quotemark": [true, "single", "jsx-double"],
        "no-any": [true, { "ignore-rest-args": true }],
        "no-empty-interface": true,
        "no-for-in": true,
        "no-console": [true, "log", "error"],
        "no-debugger": true,
        "no-duplicate-super": true,
        "no-duplicate-switch-case": true,
        "no-empty": [true, "allow-empty-catch", "allow-empty-functions"],
        "no-eval": true,
        //"no-for-in-array": true,
        "no-invalid-this": [true, "check-function-in-method"],
        "no-return-await": true,
        "no-switch-case-fall-through": true,
        "no-var-keyword": true,
        "static-this": true,
        "switch-default": true,
        "triple-equals": true,
        "no-duplicate-imports": [true, { "allow-namespace-imports": true }],
        "no-require-imports": true,
        "prefer-const": [true, { "destructuring": "all" }],
        "arrow-return-shorthand": [true, "multiline"],
        "class-name": true,
        "encoding": true,
        "prefer-while": true
        //"return-undefined": true
    },
    "defaultSeverity": "error",
    "rulesDirectory": []
}
