import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import viteTsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '');

    return {
        // depending on your application, base can also be "/"
        define: {
            'process.env.SOME_KEY': JSON.stringify(env.SOME_KEY),
            'process.env.VITE_PORT': JSON.stringify(env.VITE_PORT),
        },
        base: '/',
        plugins: [react(), viteTsconfigPaths()],
        server: {
            // this ensures that the browser opens upon server start
            open: true,
            // this sets a default port to 3000
            port: parseInt(env.VITE_PORT) || 3000,
        },
    };
});
